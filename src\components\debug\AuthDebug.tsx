'use client';

import { useAuth } from '@/lib/auth/AuthContext';
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

export default function AuthDebug() {
  const { user, profile, loading, isAdmin } = useAuth();
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [profileData, setProfileData] = useState<any>(null);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        setSessionInfo(session);
        
        if (session?.user) {
          // Try to fetch profile directly
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();
          
          if (profileError) {
            setError(`Profile fetch error: ${profileError.message}`);
          } else {
            setProfileData(profile);
          }
        }
      } catch (err) {
        setError(`Session check error: ${err}`);
      }
    };

    checkSession();
  }, []);

  return (
    <div className="fixed top-4 right-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg border max-w-md z-50">
      <h3 className="font-bold text-lg mb-2">Auth Debug Info</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>User ID:</strong> {user?.id || 'None'}
        </div>
        
        <div>
          <strong>User Email:</strong> {user?.email || 'None'}
        </div>
        
        <div>
          <strong>Profile:</strong> {profile ? JSON.stringify(profile, null, 2) : 'None'}
        </div>
        
        <div>
          <strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>Session:</strong> {sessionInfo ? 'Active' : 'None'}
        </div>
        
        <div>
          <strong>Direct Profile Fetch:</strong> 
          <pre className="text-xs mt-1 bg-gray-100 dark:bg-gray-700 p-2 rounded">
            {profileData ? JSON.stringify(profileData, null, 2) : 'None'}
          </pre>
        </div>
        
        {error && (
          <div className="text-red-600 dark:text-red-400">
            <strong>Error:</strong> {error}
          </div>
        )}
      </div>
    </div>
  );
}
