-- Chat System Database Schema Migration
-- This file contains all the necessary tables and policies for the chat system

-- 1. Extend team_applications table with account tracking
ALTER TABLE team_applications 
ADD COLUMN IF NOT EXISTS account_created BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS account_created_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id);

-- 2. Chat Permissions Table
CREATE TABLE IF NOT EXISTS chat_permissions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  application_id UUID REFERENCES team_applications(id) ON DELETE CASCADE NOT NULL,
  is_enabled BOOLEAN DEFAULT false NOT NULL,
  enabled_by UUID REFERENCES auth.users(id),
  enabled_at TIMESTAMP WITH TIME ZONE,
  disabled_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  UNIQUE(user_id, application_id)
);

-- 3. Chat Rooms Table
CREATE TABLE IF NOT EXISTS chat_rooms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  type TEXT NOT NULL CHECK (type IN ('applicant_admin', 'admin_only')),
  name TEXT,
  application_id UUID REFERENCES team_applications(id) ON DELETE CASCADE,
  created_by UUID REFERENCES auth.users(id),
  is_active BOOLEAN DEFAULT true NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 4. Chat Room Participants Table
CREATE TABLE IF NOT EXISTS chat_room_participants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES chat_rooms(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  last_read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  UNIQUE(room_id, user_id)
);

-- 5. Chat Messages Table
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES chat_rooms(id) ON DELETE CASCADE NOT NULL,
  sender_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  message TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'file', 'system')),
  file_url TEXT,
  is_edited BOOLEAN DEFAULT false NOT NULL,
  edited_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 6. Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_chat_permissions_user_id ON chat_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_permissions_application_id ON chat_permissions(application_id);
CREATE INDEX IF NOT EXISTS idx_chat_rooms_type ON chat_rooms(type);
CREATE INDEX IF NOT EXISTS idx_chat_rooms_application_id ON chat_rooms(application_id);
CREATE INDEX IF NOT EXISTS idx_chat_room_participants_room_id ON chat_room_participants(room_id);
CREATE INDEX IF NOT EXISTS idx_chat_room_participants_user_id ON chat_room_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_room_id ON chat_messages(room_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_team_applications_email ON team_applications(email);
CREATE INDEX IF NOT EXISTS idx_team_applications_user_id ON team_applications(user_id);

-- 7. Row Level Security Policies

-- Chat Permissions Policies
ALTER TABLE chat_permissions ENABLE ROW LEVEL SECURITY;

-- Users can view their own chat permissions
CREATE POLICY "Users can view own chat permissions" ON chat_permissions
  FOR SELECT USING (auth.uid() = user_id);

-- Admins can view and manage all chat permissions
CREATE POLICY "Admins can manage chat permissions" ON chat_permissions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND (profiles.role = 'admin' OR profiles.email = '<EMAIL>')
    )
  );

-- Chat Rooms Policies
ALTER TABLE chat_rooms ENABLE ROW LEVEL SECURITY;

-- Users can view rooms they participate in
CREATE POLICY "Users can view participating rooms" ON chat_rooms
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM chat_room_participants 
      WHERE chat_room_participants.room_id = chat_rooms.id 
      AND chat_room_participants.user_id = auth.uid()
    )
  );

-- Admins can manage all chat rooms
CREATE POLICY "Admins can manage chat rooms" ON chat_rooms
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND (profiles.role = 'admin' OR profiles.email = '<EMAIL>')
    )
  );

-- Chat Room Participants Policies
ALTER TABLE chat_room_participants ENABLE ROW LEVEL SECURITY;

-- Users can view their own participation records
CREATE POLICY "Users can view own participation" ON chat_room_participants
  FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own last_read_at
CREATE POLICY "Users can update own read status" ON chat_room_participants
  FOR UPDATE USING (auth.uid() = user_id);

-- Admins can manage all participants
CREATE POLICY "Admins can manage participants" ON chat_room_participants
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND (profiles.role = 'admin' OR profiles.email = '<EMAIL>')
    )
  );

-- Chat Messages Policies
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- Users can view messages in rooms they participate in
CREATE POLICY "Users can view messages in participating rooms" ON chat_messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM chat_room_participants 
      WHERE chat_room_participants.room_id = chat_messages.room_id 
      AND chat_room_participants.user_id = auth.uid()
    )
  );

-- Users can send messages to rooms they participate in
CREATE POLICY "Users can send messages to participating rooms" ON chat_messages
  FOR INSERT WITH CHECK (
    auth.uid() = sender_id AND
    EXISTS (
      SELECT 1 FROM chat_room_participants 
      WHERE chat_room_participants.room_id = chat_messages.room_id 
      AND chat_room_participants.user_id = auth.uid()
    )
  );

-- Users can edit their own messages
CREATE POLICY "Users can edit own messages" ON chat_messages
  FOR UPDATE USING (auth.uid() = sender_id);

-- 8. Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers
CREATE TRIGGER update_chat_permissions_updated_at BEFORE UPDATE ON chat_permissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chat_rooms_updated_at BEFORE UPDATE ON chat_rooms
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. Audit Logging Table
CREATE TABLE IF NOT EXISTS chat_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  table_name TEXT NOT NULL,
  operation TEXT NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
  record_id UUID NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create index for audit log queries
CREATE INDEX IF NOT EXISTS idx_chat_audit_log_table_name ON chat_audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_chat_audit_log_user_id ON chat_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_audit_log_created_at ON chat_audit_log(created_at);

-- 10. Audit Trigger Function
CREATE OR REPLACE FUNCTION chat_audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Only log for chat-related tables
  IF TG_TABLE_NAME IN ('chat_messages', 'chat_rooms', 'chat_permissions', 'chat_room_participants') THEN
    INSERT INTO chat_audit_log (
      table_name,
      operation,
      record_id,
      user_id,
      old_values,
      new_values
    ) VALUES (
      TG_TABLE_NAME,
      TG_OP,
      COALESCE(NEW.id, OLD.id),
      auth.uid(),
      CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
      CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END
    );
  END IF;

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit triggers to all chat tables
CREATE TRIGGER chat_messages_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON chat_messages
  FOR EACH ROW EXECUTE FUNCTION chat_audit_trigger();

CREATE TRIGGER chat_rooms_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON chat_rooms
  FOR EACH ROW EXECUTE FUNCTION chat_audit_trigger();

CREATE TRIGGER chat_permissions_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON chat_permissions
  FOR EACH ROW EXECUTE FUNCTION chat_audit_trigger();

CREATE TRIGGER chat_room_participants_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON chat_room_participants
  FOR EACH ROW EXECUTE FUNCTION chat_audit_trigger();

-- 11. Enhanced Security Policies

-- Additional security for chat_messages
CREATE POLICY "Prevent message editing by others" ON chat_messages
  FOR UPDATE USING (
    auth.uid() = sender_id AND
    created_at > NOW() - INTERVAL '15 minutes' -- Only allow editing within 15 minutes
  );

-- Rate limiting function (to be used in application logic)
CREATE OR REPLACE FUNCTION check_message_rate_limit(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  message_count INTEGER;
BEGIN
  -- Check if user has sent more than 50 messages in the last hour
  SELECT COUNT(*) INTO message_count
  FROM chat_messages
  WHERE sender_id = user_id
    AND created_at > NOW() - INTERVAL '1 hour';

  RETURN message_count < 50;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. Message Content Security
-- Function to sanitize message content (basic implementation)
CREATE OR REPLACE FUNCTION sanitize_message_content(content TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Remove potential XSS patterns and limit length
  RETURN LEFT(
    REGEXP_REPLACE(
      REGEXP_REPLACE(content, '<[^>]*>', '', 'g'), -- Remove HTML tags
      '[^\x20-\x7E\x0A\x0D]', '', 'g' -- Remove non-printable characters except newlines
    ),
    1000 -- Limit to 1000 characters
  );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 13. Enable Realtime for chat functionality
ALTER PUBLICATION supabase_realtime ADD TABLE chat_messages;
ALTER PUBLICATION supabase_realtime ADD TABLE chat_rooms;
ALTER PUBLICATION supabase_realtime ADD TABLE chat_room_participants;

-- 14. Additional Security Views
-- Create a secure view for chat room access
CREATE OR REPLACE VIEW secure_chat_rooms AS
SELECT
  cr.*,
  CASE
    WHEN cr.type = 'admin_only' THEN
      EXISTS(SELECT 1 FROM profiles WHERE id = auth.uid() AND (role = 'admin' OR email = '<EMAIL>'))
    ELSE
      EXISTS(SELECT 1 FROM chat_room_participants WHERE room_id = cr.id AND user_id = auth.uid())
  END AS can_access
FROM chat_rooms cr
WHERE cr.is_active = true;
